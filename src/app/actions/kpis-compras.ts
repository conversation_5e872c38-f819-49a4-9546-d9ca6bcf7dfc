"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/authOptions";
import { prisma } from "@/lib/db";
import { z } from "zod";
import { revalidatePath } from "next/cache";

// Esquema para proveedor individual
const proveedorSchema = z.object({
  nombre: z.string(), // Permitir nombres vacíos para casos especiales
  porcentaje: z.number().min(0).max(100, "El porcentaje debe estar entre 0 y 100")
}).refine((data) => {
  // Si el nombre está vacío, es válido (caso de "sin seleccionar")
  if (!data.nombre || data.nombre === "" || data.nombre === "none") {
    return true;
  }

  // Si es la opción especial "0% - Ningún proveedor", es válido
  if (data.nombre === "0% - Ningún proveedor") {
    return true;
  }

  // Para proveedores reales, el nombre debe tener al menos 1 carácter
  return data.nombre.length >= 1;
}, {
  message: "El nombre del proveedor es requerido para proveedores reales",
  path: ["nombre"]
});

// Esquema de validación para crear/actualizar KPIs semanales de compras
const kpiComprasSchema = z.object({
  year: z.number().int().min(2020).max(2030),
  weekNumber: z.number().int().min(1).max(53),
  weekStartDate: z.string().datetime(),
  weekEndDate: z.string().datetime(),
  numeroProveedoresActivos: z.number().int().min(0),
  porcentajeReporteGanancia: z.number().min(0).max(100),
  preciosPromedioCompra: z.number().min(0),
  diferencialPrecioPemex: z.number(),
  distribucionProveedores: z.array(proveedorSchema).min(1, "Debe haber al menos un proveedor")
}).refine((data) => {
  // Filtrar proveedores reales (excluyendo vacíos y opciones especiales)
  const proveedoresReales = data.distribucionProveedores.filter(p =>
    p.nombre &&
    p.nombre !== "" &&
    p.nombre !== "none" &&
    p.nombre !== "0% - Ningún proveedor"
  );

  // Si hay proveedores reales, validar que sumen 100%
  if (proveedoresReales.length > 0) {
    const totalPorcentaje = proveedoresReales.reduce((sum, p) => sum + p.porcentaje, 0);
    return Math.abs(totalPorcentaje - 100) <= 0.1;
  }

  // Si no hay proveedores reales, está bien (caso de todos vacíos o "0%")
  return true;
}, {
  message: "La suma de porcentajes de proveedores reales debe ser exactamente 100%",
  path: ["distribucionProveedores"]
});

const updateKpiComprasSchema = z.object({
  numeroProveedoresActivos: z.number().int().min(0).optional(),
  porcentajeReporteGanancia: z.number().min(0).max(100).optional(),
  preciosPromedioCompra: z.number().min(0).optional(),
  diferencialPrecioPemex: z.number().optional(),
  distribucionProveedores: z.array(proveedorSchema).min(1, "Debe haber al menos un proveedor").optional()
}).refine((data) => {
  // Si no se está actualizando la distribución de proveedores, está bien
  if (!data.distribucionProveedores) {
    return true;
  }

  // Filtrar proveedores reales (excluyendo vacíos y opciones especiales)
  const proveedoresReales = data.distribucionProveedores.filter(p =>
    p.nombre &&
    p.nombre !== "" &&
    p.nombre !== "none" &&
    p.nombre !== "0% - Ningún proveedor"
  );

  // Si hay proveedores reales, validar que sumen 100%
  if (proveedoresReales.length > 0) {
    const totalPorcentaje = proveedoresReales.reduce((sum, p) => sum + p.porcentaje, 0);
    return Math.abs(totalPorcentaje - 100) <= 0.1;
  }

  // Si no hay proveedores reales, está bien (caso de todos vacíos o "0%")
  return true;
}, {
  message: "La suma de porcentajes de proveedores reales debe ser exactamente 100%",
  path: ["distribucionProveedores"]
});

export interface ProveedorData {
  nombre: string;
  porcentaje: number;
}

export interface KpiComprasData {
  id?: string;
  year: number;
  weekNumber: number;
  weekStartDate: string;
  weekEndDate: string;
  numeroProveedoresActivos: number;
  porcentajeReporteGanancia: number;
  preciosPromedioCompra: number;
  diferencialPrecioPemex: number;
  distribucionProveedores: ProveedorData[];
}

// Verificar permisos
async function checkPermissions(allowedRoles: string[] = ["ADMIN", "SUPER_ADMIN", "WORKER"]) {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    throw new Error("No autenticado");
  }

  const userRole = session.user.role;
  if (!userRole || !allowedRoles.includes(userRole)) {
    throw new Error("Acceso denegado");
  }

  return session.user;
}

// Obtener KPIs semanales de compras
export async function getKpisCompras(filters?: {
  year?: number;
  weekNumber?: number;
  limit?: number;
}) {
  try {
    await checkPermissions();

    let whereClause: any = {};
    
    if (filters?.year) {
      whereClause.year = filters.year;
    }
    
    if (filters?.weekNumber) {
      whereClause.weekNumber = filters.weekNumber;
    }

    const kpis = await prisma.kpiCompras.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [
        { year: 'desc' },
        { weekNumber: 'desc' }
      ],
      take: filters?.limit || undefined
    });

    // Parsear el JSON de distribución de proveedores
    const kpisWithParsedData = kpis.map(kpi => ({
      ...kpi,
      distribucionProveedores: JSON.parse(kpi.distribucionProveedores)
    }));

    return { success: true, data: kpisWithParsedData };
  } catch (error) {
    console.error("Error al obtener KPIs de compras:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Crear nuevo KPI semanal de compras
export async function createKpiCompras(data: KpiComprasData) {
  try {
    const user = await checkPermissions();

    // Validar datos
    const validatedData = kpiComprasSchema.parse(data);

    // Verificar si ya existe un KPI para esa semana
    const existingKpi = await prisma.kpiCompras.findUnique({
      where: {
        year_weekNumber: {
          year: validatedData.year,
          weekNumber: validatedData.weekNumber
        }
      }
    });

    if (existingKpi) {
      return {
        success: false,
        error: "Ya existe un KPI de compras para esta semana"
      };
    }

    // Crear el KPI semanal de compras
    const kpiCompras = await prisma.kpiCompras.create({
      data: {
        year: validatedData.year,
        weekNumber: validatedData.weekNumber,
        weekStartDate: new Date(validatedData.weekStartDate),
        weekEndDate: new Date(validatedData.weekEndDate),
        numeroProveedoresActivos: validatedData.numeroProveedoresActivos,
        porcentajeReporteGanancia: validatedData.porcentajeReporteGanancia,
        preciosPromedioCompra: validatedData.preciosPromedioCompra,
        diferencialPrecioPemex: validatedData.diferencialPrecioPemex,
        distribucionProveedores: JSON.stringify(validatedData.distribucionProveedores),
        userId: user.id
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true, data: kpiCompras };
  } catch (error) {
    console.error("Error al crear KPI de compras:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Datos inválidos",
        details: error.errors
      };
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Obtener KPI de compras específico
export async function getKpiCompras(id: string) {
  try {
    await checkPermissions();

    const kpi = await prisma.kpiCompras.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!kpi) {
      return { success: false, error: "KPI de compras no encontrado" };
    }

    // Parsear el JSON de distribución de proveedores
    const kpiWithParsedData = {
      ...kpi,
      distribucionProveedores: JSON.parse(kpi.distribucionProveedores)
    };

    return { success: true, data: kpiWithParsedData };
  } catch (error) {
    console.error("Error al obtener KPI de compras:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Actualizar KPI de compras
export async function updateKpiCompras(id: string, data: Partial<KpiComprasData>) {
  try {
    const user = await checkPermissions();

    // Verificar que el KPI existe
    const existingKpi = await prisma.kpiCompras.findUnique({
      where: { id }
    });

    if (!existingKpi) {
      return { success: false, error: "KPI de compras no encontrado" };
    }

    // Solo el creador, admin o super_admin pueden actualizar
    if (user.role === "WORKER" && existingKpi.userId !== user.id) {
      return { success: false, error: "No tienes permisos para actualizar este KPI" };
    }

    // Validar datos
    const validatedData = updateKpiComprasSchema.parse(data);

    // Preparar datos para actualización
    const updateData: any = {};
    if (validatedData.numeroProveedoresActivos !== undefined) {
      updateData.numeroProveedoresActivos = validatedData.numeroProveedoresActivos;
    }
    if (validatedData.porcentajeReporteGanancia !== undefined) {
      updateData.porcentajeReporteGanancia = validatedData.porcentajeReporteGanancia;
    }
    if (validatedData.preciosPromedioCompra !== undefined) {
      updateData.preciosPromedioCompra = validatedData.preciosPromedioCompra;
    }
    if (validatedData.diferencialPrecioPemex !== undefined) {
      updateData.diferencialPrecioPemex = validatedData.diferencialPrecioPemex;
    }
    if (validatedData.distribucionProveedores !== undefined) {
      updateData.distribucionProveedores = JSON.stringify(validatedData.distribucionProveedores);
    }

    // Actualizar el KPI de compras
    const updatedKpi = await prisma.kpiCompras.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true, data: updatedKpi };
  } catch (error) {
    console.error("Error al actualizar KPI de compras:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Datos inválidos",
        details: error.errors
      };
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}

// Eliminar KPI de compras
export async function deleteKpiCompras(id: string) {
  try {
    await checkPermissions(["ADMIN", "SUPER_ADMIN"]); // Solo admins pueden eliminar

    // Verificar que el KPI existe
    const existingKpi = await prisma.kpiCompras.findUnique({
      where: { id }
    });

    if (!existingKpi) {
      return { success: false, error: "KPI de compras no encontrado" };
    }

    // Eliminar el KPI de compras
    await prisma.kpiCompras.delete({
      where: { id }
    });

    // Revalidar la página del admin para actualizar los datos
    revalidatePath('/admin');

    return { success: true, message: "KPI de compras eliminado exitosamente" };
  } catch (error) {
    console.error("Error al eliminar KPI de compras:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Error interno del servidor" 
    };
  }
}
